2025-08-11 04:08:30.775 - INFO - STARTUP_DEBUG - setup_detailed_logging:38 - ================================================================================
2025-08-11 04:08:30.775 - INFO - STARTUP_DEBUG - setup_detailed_logging:39 - WRITING TOOLS - STARTUP DEBUG SESSION
2025-08-11 04:08:30.775 - INFO - STARTUP_DEBUG - setup_detailed_logging:40 - ================================================================================
2025-08-11 04:08:30.775 - INFO - STARTUP_DEBUG - setup_detailed_logging:41 - Log file: C:\Users\<USER>\Desktop\Bureau temp\Mes_projets\copy-new\startup_logs\startup_debug_20250811_040830.log
2025-08-11 04:08:30.775 - INFO - STARTUP_DEBUG - setup_detailed_logging:42 - Python version: 3.12.0 (tags/v3.12.0:0fb18b0, Oct  2 2023, 13:03:39) [MSC v.1935 64 bit (AMD64)]
2025-08-11 04:08:30.779 - INFO - STARTUP_DEBUG - setup_detailed_logging:43 - Python executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe
2025-08-11 04:08:30.779 - INFO - STARTUP_DEBUG - setup_detailed_logging:44 - Script path: startup_debug.py
2025-08-11 04:08:30.779 - INFO - STARTUP_DEBUG - setup_detailed_logging:45 - Working directory: C:\Users\<USER>\Desktop\Bureau temp\Mes_projets\copy-new
2025-08-11 04:08:30.779 - INFO - STARTUP_DEBUG - setup_detailed_logging:46 - Frozen: False
2025-08-11 04:08:30.876 - INFO - STARTUP_DEBUG - setup_detailed_logging:56 - Platform: Windows-11-10.0.26100-SP0
2025-08-11 04:08:30.876 - INFO - STARTUP_DEBUG - setup_detailed_logging:57 - Machine: AMD64
2025-08-11 04:08:30.876 - INFO - STARTUP_DEBUG - setup_detailed_logging:58 - Processor: AMD64 Family 23 Model 104 Stepping 1, AuthenticAMD
2025-08-11 04:08:30.876 - INFO - STARTUP_DEBUG - setup_detailed_logging:66 - ENV PATH: C:\Users\<USER>\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\local\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\ffmpeg\bin;C:\Neovim\bin;C:\Program Files\dotnet;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\Program Files\Git\cmd;C:\Program Files\nodejs;C:\Program Files\PowerShell\7;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\Downloads\Compressed\platform-tools_r34.0.5-windows\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\tgpt;C:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\scripts\noConfigScripts;C:\Program Files\Git\usr\bin\vendor_perl;C:\Program Files\Git\usr\bin\core_perl
2025-08-11 04:08:30.876 - INFO - STARTUP_DEBUG - setup_detailed_logging:66 - ENV USERPROFILE: C:\Users\<USER>\Users\dd200\AppData\Roaming
2025-08-11 04:08:30.876 - INFO - STARTUP_DEBUG - setup_detailed_logging:66 - ENV LOCALAPPDATA: C:\Users\<USER>\AppData\Local
2025-08-11 04:08:30.882 - INFO - STARTUP_DEBUG - setup_detailed_logging:66 - ENV TEMP: C:\Users\<USER>\AppData\Local\Temp
2025-08-11 04:08:30.882 - INFO - STARTUP_DEBUG - main:163 - Starting Writing Tools startup debug...
2025-08-11 04:08:30.882 - WARNING - PYTHON_FINDER - find_project_python:152 - No virtual environment found, using system Python
2025-08-11 04:08:30.882 - INFO - STARTUP_DEBUG - main:167 - Using Python: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe
2025-08-11 04:08:30.882 - INFO - STARTUP_DEBUG - main:186 - Checking systray environment...
2025-08-11 04:08:30.882 - INFO - SYSTRAY_ENV - log_systray_environment:77 - Importing PySide6...
2025-08-11 04:08:30.893 - ERROR - SYSTRAY_ENV - log_systray_environment:126 - Error in systray environment check: DLL load failed while importing QtWidgets: Le module spécifié est introuvable.
2025-08-11 04:08:30.899 - ERROR - SYSTRAY_ENV - log_systray_environment:127 - Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Bureau temp\Mes_projets\copy-new\startup_debug.py", line 78, in log_systray_environment
    from PySide6 import QtWidgets, QtCore, QtGui
ImportError: DLL load failed while importing QtWidgets: Le module spécifié est introuvable.

2025-08-11 04:08:30.899 - ERROR - STARTUP_DEBUG - main:190 - Systray environment check failed!
