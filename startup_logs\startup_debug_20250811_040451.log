2025-08-11 04:04:51.973 - INFO - STARTUP_DEBUG - setup_detailed_logging:40 - ================================================================================
2025-08-11 04:04:51.974 - INFO - STARTUP_DEBUG - setup_detailed_logging:41 - WRITING TOOLS - STARTUP DEBUG SESSION
2025-08-11 04:04:51.974 - INFO - STARTUP_DEBUG - setup_detailed_logging:42 - ================================================================================
2025-08-11 04:04:51.974 - INFO - STARTUP_DEBUG - setup_detailed_logging:43 - Log file: C:\Users\<USER>\Desktop\Bureau temp\Mes_projets\copy-new\startup_logs\startup_debug_20250811_040451.log
2025-08-11 04:04:51.975 - INFO - STARTUP_DEBUG - setup_detailed_logging:44 - Python version: 3.12.0 (tags/v3.12.0:0fb18b0, Oct  2 2023, 13:03:39) [MSC v.1935 64 bit (AMD64)]
2025-08-11 04:04:51.975 - INFO - STARTUP_DEBUG - setup_detailed_logging:45 - Python executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe
2025-08-11 04:04:51.975 - INFO - STARTUP_DEBUG - setup_detailed_logging:46 - Script path: startup_debug.py
2025-08-11 04:04:51.975 - INFO - STARTUP_DEBUG - setup_detailed_logging:47 - Working directory: C:\Users\<USER>\Desktop\Bureau temp\Mes_projets\copy-new
2025-08-11 04:04:51.975 - INFO - STARTUP_DEBUG - setup_detailed_logging:48 - Frozen: False
2025-08-11 04:04:52.089 - INFO - STARTUP_DEBUG - setup_detailed_logging:57 - Platform: Windows-11-10.0.26100-SP0
2025-08-11 04:04:52.089 - INFO - STARTUP_DEBUG - setup_detailed_logging:58 - Machine: AMD64
2025-08-11 04:04:52.089 - INFO - STARTUP_DEBUG - setup_detailed_logging:59 - Processor: AMD64 Family 23 Model 104 Stepping 1, AuthenticAMD
2025-08-11 04:04:52.090 - INFO - STARTUP_DEBUG - setup_detailed_logging:67 - ENV PATH: C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\ffmpeg\bin;C:\Neovim\bin;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\Program Files\PowerShell\7\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\Downloads\Compressed\platform-tools_r34.0.5-windows\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\tgpt
2025-08-11 04:04:52.090 - INFO - STARTUP_DEBUG - setup_detailed_logging:67 - ENV USERPROFILE: C:\Users\<USER>\Users\dd200\AppData\Roaming
2025-08-11 04:04:52.090 - INFO - STARTUP_DEBUG - setup_detailed_logging:67 - ENV LOCALAPPDATA: C:\Users\<USER>\AppData\Local
2025-08-11 04:04:52.090 - INFO - STARTUP_DEBUG - setup_detailed_logging:67 - ENV TEMP: C:\Users\<USER>\AppData\Local\Temp
2025-08-11 04:04:52.090 - INFO - STARTUP_DEBUG - main:136 - Starting Writing Tools startup debug...
2025-08-11 04:04:52.091 - INFO - STARTUP_DEBUG - main:139 - Checking systray environment...
2025-08-11 04:04:52.091 - INFO - SYSTRAY_ENV - log_systray_environment:77 - Importing PySide6...
2025-08-11 04:04:52.111 - ERROR - SYSTRAY_ENV - log_systray_environment:125 - Error in systray environment check: DLL load failed while importing QtWidgets: Le module spécifié est introuvable.
2025-08-11 04:04:52.117 - ERROR - SYSTRAY_ENV - log_systray_environment:126 - Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Bureau temp\Mes_projets\copy-new\startup_debug.py", line 78, in log_systray_environment
    from PySide6 import QtWidgets, QtCore, QtGui
ImportError: DLL load failed while importing QtWidgets: Le module spécifié est introuvable.

2025-08-11 04:04:52.117 - ERROR - STARTUP_DEBUG - main:143 - Systray environment check failed!
