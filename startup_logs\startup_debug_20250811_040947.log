2025-08-11 04:09:47.134 - INFO - STARTUP_DEBUG - setup_detailed_logging:38 - ================================================================================
2025-08-11 04:09:47.134 - INFO - STARTUP_DEBUG - setup_detailed_logging:39 - WRITING TOOLS - STARTUP DEBUG SESSION
2025-08-11 04:09:47.134 - INFO - STARTUP_DEBUG - setup_detailed_logging:40 - ================================================================================
2025-08-11 04:09:47.134 - INFO - STARTUP_DEBUG - setup_detailed_logging:41 - Log file: C:\Users\<USER>\Desktop\Bureau temp\Mes_projets\copy-new\startup_logs\startup_debug_20250811_040947.log
2025-08-11 04:09:47.134 - INFO - STARTUP_DEBUG - setup_detailed_logging:42 - Python version: 3.12.0 (tags/v3.12.0:0fb18b0, Oct  2 2023, 13:03:39) [MSC v.1935 64 bit (AMD64)]
2025-08-11 04:09:47.134 - INFO - STARTUP_DEBUG - setup_detailed_logging:43 - Python executable: C:\Users\<USER>\Desktop\Bureau temp\Mes_projets\copy-new\Windows_and_Linux\myvenv\Scripts\python.exe
2025-08-11 04:09:47.135 - INFO - STARTUP_DEBUG - setup_detailed_logging:44 - Script path: C:\Users\<USER>\Desktop\Bureau temp\Mes_projets\copy-new\startup_debug.py
2025-08-11 04:09:47.135 - INFO - STARTUP_DEBUG - setup_detailed_logging:45 - Working directory: C:\Users\<USER>\Desktop\Bureau temp\Mes_projets\copy-new
2025-08-11 04:09:47.135 - INFO - STARTUP_DEBUG - setup_detailed_logging:46 - Frozen: False
2025-08-11 04:09:47.222 - INFO - STARTUP_DEBUG - setup_detailed_logging:56 - Platform: Windows-11-10.0.26100-SP0
2025-08-11 04:09:47.222 - INFO - STARTUP_DEBUG - setup_detailed_logging:57 - Machine: AMD64
2025-08-11 04:09:47.222 - INFO - STARTUP_DEBUG - setup_detailed_logging:58 - Processor: AMD64 Family 23 Model 104 Stepping 1, AuthenticAMD
2025-08-11 04:09:47.222 - INFO - STARTUP_DEBUG - setup_detailed_logging:66 - ENV PATH: C:\Users\<USER>\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\local\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\ffmpeg\bin;C:\Neovim\bin;C:\Program Files\dotnet;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\Program Files\Git\cmd;C:\Program Files\nodejs;C:\Program Files\PowerShell\7;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\Downloads\Compressed\platform-tools_r34.0.5-windows\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\tgpt;C:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\scripts\noConfigScripts;C:\Program Files\Git\usr\bin\vendor_perl;C:\Program Files\Git\usr\bin\core_perl
2025-08-11 04:09:47.222 - INFO - STARTUP_DEBUG - setup_detailed_logging:66 - ENV USERPROFILE: C:\Users\<USER>\Users\dd200\AppData\Roaming
2025-08-11 04:09:47.222 - INFO - STARTUP_DEBUG - setup_detailed_logging:66 - ENV LOCALAPPDATA: C:\Users\<USER>\AppData\Local
2025-08-11 04:09:47.222 - INFO - STARTUP_DEBUG - setup_detailed_logging:66 - ENV TEMP: C:\Users\<USER>\AppData\Local\Temp
2025-08-11 04:09:47.222 - INFO - STARTUP_DEBUG - main:165 - Starting Writing Tools startup debug...
2025-08-11 04:09:47.222 - INFO - PYTHON_FINDER - find_project_python:150 - Found virtual environment Python: C:\Users\<USER>\Desktop\Bureau temp\Mes_projets\copy-new\Windows_and_Linux\myvenv\Scripts\python.exe
2025-08-11 04:09:47.222 - INFO - STARTUP_DEBUG - main:169 - Using Python: C:\Users\<USER>\Desktop\Bureau temp\Mes_projets\copy-new\Windows_and_Linux\myvenv\Scripts\python.exe
2025-08-11 04:09:47.222 - INFO - STARTUP_DEBUG - main:188 - Checking systray environment...
2025-08-11 04:09:47.223 - INFO - SYSTRAY_ENV - log_systray_environment:77 - Importing PySide6...
2025-08-11 04:09:47.374 - INFO - SYSTRAY_ENV - log_systray_environment:80 - PySide6 imported successfully
2025-08-11 04:09:47.374 - INFO - SYSTRAY_ENV - log_systray_environment:83 - Creating temporary QApplication...
2025-08-11 04:09:47.405 - INFO - SYSTRAY_ENV - log_systray_environment:87 - New QApplication created
2025-08-11 04:09:47.406 - INFO - SYSTRAY_ENV - log_systray_environment:92 - Testing system tray availability...
2025-08-11 04:09:47.407 - INFO - SYSTRAY_ENV - log_systray_environment:94 - System tray available: True
2025-08-11 04:09:47.407 - INFO - SYSTRAY_ENV - log_systray_environment:97 - Screen information:
2025-08-11 04:09:47.407 - INFO - SYSTRAY_ENV - log_systray_environment:99 - Number of screens: 1
2025-08-11 04:09:47.407 - INFO - SYSTRAY_ENV - log_systray_environment:101 - Screen 0: \\.\DISPLAY1 - PySide6.QtCore.QRect(0, 0, 1298, 730)
2025-08-11 04:09:47.407 - INFO - SYSTRAY_ENV - log_systray_environment:105 - Attempting to create test system tray icon...
2025-08-11 04:09:47.908 - INFO - SYSTRAY_ENV - log_systray_environment:114 - Test tray icon visible: True
2025-08-11 04:09:47.908 - INFO - SYSTRAY_ENV - log_systray_environment:117 - Test tray icon cleaned up
2025-08-11 04:09:47.908 - INFO - STARTUP_DEBUG - main:196 - Launching main Writing Tools application...
2025-08-11 04:09:47.909 - INFO - STARTUP_DEBUG - main:203 - Added to path: C:\Users\<USER>\Desktop\Bureau temp\Mes_projets\copy-new\Windows_and_Linux
2025-08-11 04:09:47.909 - INFO - STARTUP_DEBUG - main:206 - Cleaning up temporary QApplication...
2025-08-11 04:09:51.078 - INFO - STARTUP_DEBUG - main:217 - Creating WritingToolApp instance...
2025-08-11 04:09:51.078 - ERROR - STARTUP_DEBUG - main:250 - Critical error in startup debug: Please destroy the QApplication singleton before creating a new WritingToolApp instance.
2025-08-11 04:09:51.080 - ERROR - STARTUP_DEBUG - main:251 - Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Bureau temp\Mes_projets\copy-new\startup_debug.py", line 218, in main
    app = WritingToolApp(sys.argv)
          ^^^^^^^^^^^^^^^^^^^^^^^^
RuntimeError: Please destroy the QApplication singleton before creating a new WritingToolApp instance.

